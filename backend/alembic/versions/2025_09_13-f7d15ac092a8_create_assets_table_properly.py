"""create_assets_table_properly

Revision ID: f7d15ac092a8
Revises: 70697579bafa
Create Date: 2025-09-13 07:29:37.487274

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f7d15ac092a8'
down_revision: Union[str, Sequence[str], None] = '70697579bafa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('assets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('product_external_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('src', sa.String(), nullable=False),
    sa.Column('alt', sa.String(), nullable=True),
    sa.Column('external_id', sa.String(), nullable=True),
    sa.Column('width', sa.Integer(), nullable=True),
    sa.Column('height', sa.Integer(), nullable=True),
    sa.Column('position', sa.Integer(), nullable=True),
    sa.Column('source_type', sa.String(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('src')
    )
    op.create_index(op.f('ix_assets_id'), 'assets', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_assets_id'), table_name='assets')
    op.drop_table('assets')
    # ### end Alembic commands ###
