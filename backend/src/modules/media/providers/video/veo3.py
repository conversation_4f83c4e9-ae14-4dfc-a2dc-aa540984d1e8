"""
Google Veo 3 Provider Plugin for E-commerce Video Generation.
Provides professional product video generation using Google's Veo 3 AI.
Specialized for product showcases, lifestyle videos, and social media content.
"""

import time
import logging
import asyncio
import os
import mimetypes
from typing import Dict, List, Optional, Any

from google import genai
from google.genai import types

from ..base import VideoProvider
from ..config import ProviderConfig
from core.config import get_settings
from ...schemas import ProviderMediaRequest, ProviderMediaResult, ProductCategory
from ...engines.context_engine import context_engine
from ...engines.prompt_engine import prompt_engine, MediaType, PromptContext, Platform

logger = logging.getLogger(__name__)


class Veo3Provider(VideoProvider):
    """Google Veo 3 provider plugin for video generation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.config: Optional[ProviderConfig] = None
        self.model = None  # Will be set from config during initialization

    @property
    def provider_name(self) -> str:
        return "veo3"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Veo 3 provider."""
        # Get API key from settings
        settings = get_settings()
        api_key = settings.VEO3_API_KEY

        if not api_key:
            logger.error("VEO3_API_KEY not configured in settings")
            return False

        try:
            # Store config and get model from config - required, no fallback
            self.config = config
            self.model = config.model

            # Additional Veo 3-specific initialization
            self.client = genai.Client(
                http_options={"api_version": "v1beta"},
                api_key=api_key
            )

            logger.info(f"Initialized Veo 3 provider with model: {self.model}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Veo 3 provider: {e}")
            return False

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce videos using Google Veo 3."""
        if not self.client or not self.config:
            return ProviderMediaResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            # Generate professional video prompts using the base class method
            video_concepts = await self._generate_professional_prompts(request)

            # Generate videos for each concept
            all_variants = []

            for i, concept in enumerate(video_concepts):
                # Extract generation settings
                settings = request.settings or {}
                custom_config = request.custom_config or {}

                # Create video config based on settings
                aspect_ratio = concept["aspect_ratio"]
                # Veo 3.0 model only supports exactly 8 seconds duration
                duration_seconds = 8

                video_config = types.GenerateVideosConfig(
                    aspect_ratio=aspect_ratio,  # Keep as-is, API expects "16:9" format
                    number_of_videos=1,
                    duration_seconds=duration_seconds,
                    negative_prompt="cartoon, drawing, low quality, blurry, distorted, amateur, watermark, text overlay"
                )

                # Prepare video generation parameters
                video_params = {
                    "model": self.model,
                    "prompt": concept["prompt"],
                    "config": video_config,
                }

                # Add image input if provided
                if request.reference_image:
                    try:
                        # Decode base64 image data
                        import base64
                        image_data = base64.b64decode(request.reference_image.bytesBase64Encoded)

                        # Create Image object with the binary data and mime type
                        input_image = types.Image(image_bytes=image_data, mime_type=request.reference_image.mimeType)

                        video_params["image"] = input_image
                        logger.info("Using processed image for video generation")
                    except Exception as e:
                        logger.error(f"Failed to process reference image: {e}")
                        raise
                elif request.reference_image_url:
                    try:
                        image_data = await self.download_media(request.reference_image_url)

                        # Derive mime type from URL extension
                        mime_type, _ = mimetypes.guess_type(request.reference_image_url)
                        if not mime_type:
                            mime_type = 'image/jpeg'  # fallback to jpeg if unable to determine

                        # Create an Image object with the binary data and mime type
                        input_image = types.Image(image_bytes=image_data, mime_type=mime_type)

                        video_params["image"] = input_image
                        logger.info(f"Using downloaded image for video generation (mime_type: {mime_type})")
                    except Exception as e:
                        logger.warning(f"Failed to download reference image: {e}")
                        # Continue without image input

                # Generate video using Google GenAI SDK
                operation = self.client.models.generate_videos(**video_params)

                # Wait for the video(s) to be generated
                while not operation.done:
                    logger.info("Video has not been generated yet. Check again in 10 seconds...")
                    time.sleep(10)
                    operation = self.client.operations.get(operation)

                # Access the generated videos from the operation response
                generated_videos = operation.response.generated_videos
                if not generated_videos:
                    logger.error("No videos were generated.")
                    continue

                logger.info(f"Generated {len(generated_videos)} video(s).")
                for n, generated_video in enumerate(generated_videos):
                    video_url = generated_video.video.uri
                    logger.info(f"Video has been generated: {video_url}")

                    # Download the video
                    try:
                        self.client.files.download(file=generated_video.video)
                        local_path = f"video_{i}_{n}.mp4"
                        generated_video.video.save(local_path)
                        logger.info(f"Video {video_url} has been downloaded to {local_path}.")
                    except Exception as e:
                        logger.warning(f"Failed to download video: {e}")
                        local_path = video_url  # Use remote URL if download fails

                    # Process generated video
                    all_variants.append({
                        "variant_name": concept["variant_name"],
                        "video_url": local_path if local_path != video_url else video_url,
                        "thumbnail_url": generated_video.video.uri.replace('.mp4', '_thumb.jpg'),  # Placeholder
                        "duration": duration_seconds,
                        "resolution": self._get_resolution_for_aspect(concept["aspect_ratio"]),
                        "aspect_ratio": concept["aspect_ratio"],
                        "style_type": concept["style_type"],
                        "prompt_used": concept["prompt"],
                        "generation_metadata": {
                            "provider": "veo3",
                            "model": self.model,
                            "style_type": concept.get("style_type", "product_showcase"),
                            "target_audience": concept.get("target_audience"),
                            "platform_optimized": concept.get("platform")
                        }
                    })

                # Add delay between requests
                await asyncio.sleep(1.0)

            return ProviderMediaResult(
                success=True,
                provider_job_id=f"veo3_batch_{hash(request.product_title)}",
                variants=all_variants[:request.variants_count],
                estimated_completion_time=len(video_concepts) * 180
            )

        except Exception as e:
            logger.error(f"Veo 3 generation failed: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=str(e)
            )


    async def download_media(self, media_url: str) -> bytes:
        """Download video from Veo 3."""
        if not self.client:
            raise ValueError("Provider not initialized")

        # For local files, read from disk
        if media_url.startswith("/") or media_url.startswith("./"):
            with open(media_url, "rb") as f:
                return f.read()

        # For remote URLs, download
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(media_url)
            response.raise_for_status()
            return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Veo 3 provider information."""
        return {
            "name": self.config.metadata.provider_name,
            "description": self.config.metadata.description,
            "supported_formats": self.config.capabilities.supported_formats,
            "models": [self.config.model],
            "max_variants_per_request": self.config.limits.max_variants_per_request,
            "supported_aspect_ratios": self.config.capabilities.supported_aspect_ratios,
            "max_duration_seconds": self.config.capabilities.max_duration_seconds,
            "supported_person_generation": self.config.capabilities.supported_person_generation,
            "features": self.config.features,
            "estimated_cost_per_video": self.config.costs.cost_per_unit,
            "average_generation_time": self.config.quality.average_generation_time,
            "quality_score": self.config.quality.quality_score
        }

    async def cleanup(self) -> None:
        """Cleanup Veo 3 provider resources."""
        if self.client:
            # Google GenAI client doesn't need explicit cleanup
            self.client = None
            logger.info("Cleaned up Veo 3 provider")