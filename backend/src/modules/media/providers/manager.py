"""
Provider Manager for Media Generation Providers.
Handles dynamic loading and management of media providers.
"""

import importlib
import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Type

from .base import (
    BaseMediaProvider,
    ProviderConfig
)

logger = logging.getLogger(__name__)


class MediaProviderRegistry:
    """Registry for managing media provider plugins."""

    def __init__(self):
        self._providers: Dict[str, BaseMediaProvider] = {}
        self._configs: Dict[str, ProviderConfig] = {}

    def register_provider(self, provider: BaseMediaProvider, config: ProviderConfig):
        """Register a provider plugin."""
        self._providers[provider.provider_name] = provider
        self._configs[provider.provider_name] = config

    def unregister_provider(self, provider_name: str):
        """Unregister a provider plugin."""
        if provider_name in self._providers:
            del self._providers[provider_name]
            del self._configs[provider_name]

    def get_provider(self, provider_name: str) -> Optional[BaseMediaProvider]:
        """Get a registered provider."""
        return self._providers.get(provider_name)

    def get_available_providers(self, media_type: Optional[str] = None) -> List[str]:
        """Get list of available providers, optionally filtered by media type."""
        if media_type:
            return [
                name for name, provider in self._providers.items()
                if media_type in provider.supported_media_types
            ]
        return list(self._providers.keys())

    def get_provider_config(self, provider_name: str) -> Optional[ProviderConfig]:
        """Get configuration for a provider."""
        return self._configs.get(provider_name)

    async def initialize_all_providers(self) -> Dict[str, bool]:
        """Initialize all registered providers."""
        results = {}
        for name, provider in self._providers.items():
            config = self._configs.get(name)
            if config:
                results[name] = await provider.initialize(config)
            else:
                results[name] = False
        return results

    async def cleanup_all_providers(self) -> None:
        """Cleanup all registered providers."""
        for provider in self._providers.values():
            await provider.cleanup()


class MediaProviderManager:
    """Manager for loading and managing media providers."""

    def __init__(self, plugins_dir: Optional[str] = None):
        self.plugins_dir = Path(plugins_dir) if plugins_dir else Path(__file__).parent / "providers"
        self.plugins_dir.mkdir(exist_ok=True)
        self._loaded_plugins: Dict[str, Type[BaseMediaProvider]] = {}

    def discover_providers(self) -> List[str]:
        """Discover available media providers."""
        plugins = []

        # Look for provider modules in the providers directory and subdirectories
        if self.plugins_dir.exists():
            for item in self.plugins_dir.iterdir():
                if item.is_file() and item.suffix == '.py' and not item.name.startswith('_'):
                    plugin_name = item.stem
                    plugins.append(plugin_name)
                elif item.is_dir() and not item.name.startswith('_'):
                    # Check subdirectories for provider files
                    for sub_item in item.iterdir():
                        if sub_item.is_file() and sub_item.suffix == '.py' and not sub_item.name.startswith('_'):
                            plugin_name = f"{item.name}.{sub_item.stem}"
                            plugins.append(plugin_name)

        logger.info(f"Discovered {len(plugins)} providers: {plugins}")
        return plugins

    async def load_provider(self, provider_name: str) -> Optional[Type[BaseMediaProvider]]:
        """Load a media provider by name."""
        try:
            # Try to import the plugin module
            # Handle both direct files and files in subdirectories
            if '.' in provider_name:
                # Provider in subdirectory (e.g., "image.banana")
                module_path = f"modules.media.providers.{provider_name}"
            else:
                # Provider in main directory
                module_path = f"modules.media.providers.{provider_name}"

            module = importlib.import_module(module_path)

            # Look for the provider class
            provider_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and
                    issubclass(attr, BaseMediaProvider) and
                    attr != BaseMediaProvider):
                    provider_class = attr
                    break

            if provider_class:
                self._loaded_plugins[provider_name] = provider_class
                logger.info(f"Loaded provider plugin: {provider_name}")
                return provider_class
            else:
                logger.error(f"No provider class found in plugin: {provider_name}")
                return None

        except ImportError as e:
            logger.error(f"Failed to import plugin {provider_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error loading plugin {provider_name}: {e}")
            return None

    async def load_all_providers(self) -> Dict[str, bool]:
        """Load all discovered providers."""
        providers = self.discover_providers()
        results = {}

        for provider_name in providers:
            provider_class = await self.load_provider(provider_name)
            results[provider_name] = provider_class is not None

        return results

    def get_provider_class(self, provider_name: str) -> Optional[Type[BaseMediaProvider]]:
        """Get a loaded provider class."""
        return self._loaded_plugins.get(provider_name)

    async def create_provider_instance(
        self,
        provider_name: str,
        config: ProviderConfig
    ) -> Optional[BaseMediaProvider]:
        """Create an instance of a media provider."""
        provider_class = self.get_provider_class(provider_name)
        if not provider_class:
            logger.error(f"Provider not loaded: {provider_name}")
            return None

        try:
            provider_instance = provider_class()
            success = await provider_instance.initialize(config)

            if success:
                provider_registry.register_provider(provider_instance, config)
                logger.info(f"Registered provider instance: {provider_name}")
                return provider_instance
            else:
                logger.error(f"Failed to initialize provider: {provider_name}")
                return None

        except Exception as e:
            logger.error(f"Error creating provider instance {provider_name}: {e}")
            return None

    def get_loaded_providers(self) -> List[str]:
        """Get list of loaded provider names."""
        return list(self._loaded_plugins.keys())

    async def unload_provider(self, provider_name: str) -> bool:
        """Unload a provider."""
        if provider_name in self._loaded_plugins:
            # Remove from registry if registered
            provider = provider_registry.get_provider(provider_name)
            if provider:
                await provider.cleanup()
                provider_registry.unregister_provider(provider_name)

            del self._loaded_plugins[provider_name]
            logger.info(f"Unloaded provider: {provider_name}")
            return True

        return False

    async def reload_provider(self, provider_name: str) -> bool:
        """Reload a provider."""
        # Unload first
        await self.unload_provider(provider_name)

        # Reload
        provider_class = await self.load_provider(provider_name)
        return provider_class is not None


# Load configuration from JSON file
config_path = Path(__file__).parent.parent.parent.parent / "core" / "configs" / "providers_config.json"
with open(config_path, 'r') as f:
    config = json.load(f)

# Replace environment variable placeholders in API keys
for provider_name, provider_config in config["providers"].items():
    api_key = provider_config.get("api_key", "")
    if api_key.startswith("${") and api_key.endswith("}"):
        env_var = api_key[2:-1]
        actual_key = os.getenv(env_var)
        if actual_key:
            provider_config["api_key"] = actual_key


# Global provider manager instance
provider_manager = MediaProviderManager()

# Global provider registry instance
provider_registry = MediaProviderRegistry()


async def initialize_providers(configs: Dict[str, ProviderConfig]) -> Dict[str, bool]:
    """
    Initialize media providers from configuration.

    Args:
        configs: Dictionary mapping provider names to their configurations

    Returns:
        Dictionary with initialization results
    """
    results = {}

    # Load all available providers
    load_results = await provider_manager.load_all_providers()

    # Initialize configured providers
    for provider_name, config in configs.items():
        if provider_name in load_results and load_results[provider_name]:
            provider_instance = await provider_manager.create_provider_instance(provider_name, config)
            results[provider_name] = provider_instance is not None
        else:
            logger.warning(f"Provider {provider_name} not available or failed to load")
            results[provider_name] = False

    return results