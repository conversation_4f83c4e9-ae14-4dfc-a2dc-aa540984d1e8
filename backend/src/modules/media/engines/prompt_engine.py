"""
Prompt engineering System for E-commerce Media Generation.
Creates sophisticated prompts for images, videos, and copy that rival professional agencies.
Enhanced with comprehensive template system and brand voice integration.
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from .context_engine import ProductContext, BrandContext, MarketContext, ContentStyle, TargetAudience

logger = logging.getLogger(__name__)


class MediaType(str, Enum):
    """Types of media content."""
    PRODUCT_PHOTOGRAPHY = "product_photography"
    LIFESTYLE_PHOTOGRAPHY = "lifestyle_photography"
    PRODUCT_VIDEO = "product_video"
    LIFESTYLE_VIDEO = "lifestyle_video"
    SOCIAL_VIDEO = "social_video"
    MARKETING_COPY = "marketing_copy"
    SOCIAL_CAPTION = "social_caption"
    PRODUCT_DESCRIPTION = "product_description"


class Platform(str, Enum):
    """Target platforms for content."""
    INSTAGRAM = "instagram"
    TIKTOK = "tiktok"
    FACEBOOK = "facebook"
    PINTEREST = "pinterest"
    YOUTUBE = "youtube"
    WEBSITE = "website"
    EMAIL = "email"
    PRINT = "print"


@dataclass
class PromptContext:
    """Context for prompt generation."""
    media_type: MediaType
    platform: Optional[Platform] = None
    aspect_ratio: str = "1:1"
    duration_seconds: Optional[int] = None
    style_preference: Optional[ContentStyle] = None
    campaign_theme: Optional[str] = None
    call_to_action: Optional[str] = None
    brand_mentions: bool = True
    price_display: bool = False


@dataclass
class GeneratedPrompt:
    """Generated prompt with metadata."""
    main_prompt: str
    negative_prompt: Optional[str] = None
    style_modifiers: List[str] = None
    technical_specs: Dict[str, Any] = None
    estimated_quality_score: float = 0.0
    target_keywords: List[str] = None
    
    def __post_init__(self):
        if self.style_modifiers is None:
            self.style_modifiers = []
        if self.technical_specs is None:
            self.technical_specs = {}
        if self.target_keywords is None:
            self.target_keywords = []


class PromptEngine:
    """
    Prompt engineering system for e-commerce media generation.
    Creates agency-quality prompts for different media types and platforms.
    """
    
    def __init__(self):
        self.templates = self._load_prompt_templates()
        self.photography_styles = self._load_photography_styles()
        self.video_templates = self._load_video_templates()
        self.copy_frameworks = self._load_copy_frameworks()
        self.platform_specs = self._load_platform_specifications()

    def _load_prompt_templates(self) -> Dict[str, Any]:
        """Load comprehensive prompt templates from modular JSON files."""
        try:
            # Look in the media/prompts directory (not engines/prompts)
            prompts_dir = Path(__file__).parent.parent / "prompts"
            templates = {}

            # Load text templates (most commonly used)
            text_templates_path = prompts_dir / "text_templates.json"
            if text_templates_path.exists():
                with open(text_templates_path, 'r', encoding='utf-8') as f:
                    templates['text_templates'] = json.load(f)
                logger.info("Loaded text templates from prompts/text_templates.json")
            else:
                logger.warning("text_templates.json not found")
                templates['text_templates'] = self._get_fallback_text_templates()

            # Load image templates
            image_templates_path = prompts_dir / "image_templates.json"
            if image_templates_path.exists():
                with open(image_templates_path, 'r', encoding='utf-8') as f:
                    templates['image_templates'] = json.load(f)
                logger.info("Loaded image templates from prompts/image_templates.json")

            # Load video templates
            video_templates_path = prompts_dir / "video_templates.json"
            if video_templates_path.exists():
                with open(video_templates_path, 'r', encoding='utf-8') as f:
                    templates['video_templates'] = json.load(f)
                logger.info("Loaded video templates from prompts/video_templates.json")

            # Load brand voice templates
            brand_templates_path = prompts_dir / "brand_voice_templates.json"
            if brand_templates_path.exists():
                with open(brand_templates_path, 'r', encoding='utf-8') as f:
                    templates['brand_voice_templates'] = json.load(f)
                logger.info("Loaded brand voice templates from prompts/brand_voice_templates.json")

            return templates

        except Exception as e:
            logger.warning(f"Failed to load prompt templates: {e}")
            return self._get_fallback_templates()

    def _get_fallback_text_templates(self) -> Dict[str, Any]:
        """Get fallback text templates."""
        return {
            "product_description": {
                "structure": {
                    "hook": "Attention-grabbing opening line",
                    "features": "Key features and specifications",
                    "benefits": "Customer benefits and value proposition",
                    "social_proof": "Trust signals and credibility",
                    "call_to_action": "Clear next step for customer"
                }
            },
            "marketing_copy": {
                "ad_copy": {
                    "headline_formulas": [
                        "How to {achieve_benefit} with {product_title}",
                        "The {adjective} {product_title} that {unique_benefit}",
                        "{number} Reasons Why {product_title} is {superlative}"
                    ]
                }
            },
            "social_caption": {
                "instagram": {
                    "style": "visual-first, hashtag-friendly, engaging",
                    "length": "125-150 characters for optimal engagement"
                }
            },
            "seo_snippet": {
                "meta_descriptions": {
                    "template": "{primary_keyword} - {value_proposition}. {secondary_benefit}. {call_to_action}",
                    "length": "150-160 characters"
                }
            }
        }

    def _get_fallback_templates(self) -> Dict[str, Any]:
        """Provide fallback templates if JSON files are not available."""
        return {
            "text_templates": self._get_fallback_text_templates(),
            "image_templates": {
                "product_photography": {
                    "base_template": "Professional product photography of {product_title}",
                    "style_modifiers": {
                        "minimalist": "clean white background, minimal composition, studio lighting",
                        "luxury": "premium setting, sophisticated lighting, high-end presentation",
                        "lifestyle": "natural environment, lifestyle context, authentic setting"
                    }
                }
            },
            "video_templates": {
                "product_showcase": {
                    "base_template": "Professional product video showcasing {product_title}",
                    "shot_sequences": {
                        "hero_shot": "Opening hero shot establishing the product",
                        "detail_shots": "Close-up shots highlighting key features"
                    }
                }
            }
        }
    
    async def generate_prompt(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext] = None,
        market_context: Optional[MarketContext] = None
    ) -> GeneratedPrompt:
        """
        Generate professional prompt for media creation.
        
        Args:
            product_context: Product information and analysis
            prompt_context: Media type and platform requirements
            brand_context: Brand guidelines and preferences
            market_context: Market trends and competitive data
            
        Returns:
            Professional prompt optimized for the specific use case
        """
        if prompt_context.media_type in [MediaType.PRODUCT_PHOTOGRAPHY, MediaType.LIFESTYLE_PHOTOGRAPHY]:
            return await self._generate_photography_prompt(
                product_context, prompt_context, brand_context, market_context
            )
        elif prompt_context.media_type in [MediaType.PRODUCT_VIDEO, MediaType.LIFESTYLE_VIDEO, MediaType.SOCIAL_VIDEO]:
            return await self._generate_video_prompt(
                product_context, prompt_context, brand_context, market_context
            )
        elif prompt_context.media_type in [MediaType.MARKETING_COPY, MediaType.SOCIAL_CAPTION, MediaType.PRODUCT_DESCRIPTION]:
            return await self._generate_copy_prompt(
                product_context, prompt_context, brand_context, market_context
            )
        else:
            raise ValueError(f"Unsupported media type: {prompt_context.media_type}")
    
    async def _generate_photography_prompt(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext],
        market_context: Optional[MarketContext]
    ) -> GeneratedPrompt:
        """Generate professional photography prompt using enhanced templates."""

        # Get appropriate template
        if prompt_context.media_type == MediaType.PRODUCT_PHOTOGRAPHY:
            template_key = "product_photography"
        else:
            template_key = "lifestyle_photography"

        template = self.templates.get("image_templates", {}).get(template_key, {})

        # Base product description
        product_desc = self._create_product_description(product_context)

        # Get base template and customize
        base_template = template.get("base_template", "Professional photograph of {product_title}")
        base_prompt = base_template.format(product_title=product_context.title)

        # Determine style based on brand context and content style
        style_modifier = self._get_style_modifier_from_template(
            template, brand_context, prompt_context.style_preference
        )

        # Get category-specific setup
        category_setup = self._get_category_specific_setup(template, product_context)

        # Construct enhanced prompt
        prompt_parts = [base_prompt]

        if style_modifier:
            prompt_parts.append(style_modifier)

        if category_setup:
            prompt_parts.extend([
                category_setup.get("setup", ""),
                category_setup.get("lighting", ""),
                category_setup.get("background", ""),
                category_setup.get("composition", "")
            ])

        # Add brand-specific elements
        brand_elements = self._get_brand_visual_elements(brand_context) if brand_context else []
        if brand_elements:
            prompt_parts.append(f"Brand elements: {', '.join(brand_elements)}")

        # Add quality modifiers from template
        quality_modifiers = template.get("quality_modifiers", [
            "8K resolution", "sharp focus", "professional lighting", "commercial quality"
        ])

        # Combine all parts
        main_prompt = ", ".join([part for part in prompt_parts if part] + quality_modifiers)
        
        # Negative prompt
        negative_prompt = self._generate_photography_negative_prompt(product_context)
        
        # Technical specifications
        technical_specs = {
            "resolution": "4K",
            "color_space": "sRGB",
            "lighting_setup": lighting,
            "recommended_camera": style_elements.get("camera", "DSLR"),
            "post_processing": style_elements.get("post_processing", "minimal")
        }
        
        return GeneratedPrompt(
            main_prompt=main_prompt,
            negative_prompt=negative_prompt,
            style_modifiers=style_elements.get("modifiers", []),
            technical_specs=technical_specs,
            estimated_quality_score=self._calculate_quality_score(product_context, prompt_context),
            target_keywords=self._extract_target_keywords(product_context)
        )

    def _get_style_modifier_from_template(
        self,
        template: Dict[str, Any],
        brand_context: Optional[BrandContext],
        style_preference: Optional[ContentStyle]
    ) -> str:
        """Get style modifier from template based on brand and preferences."""
        style_modifiers = template.get("style_modifiers", {})

        if brand_context and brand_context.visual_style:
            style_key = brand_context.visual_style.value.lower()
            if style_key in style_modifiers:
                return style_modifiers[style_key]

        if style_preference:
            style_key = style_preference.value.lower()
            if style_key in style_modifiers:
                return style_modifiers[style_key]

        # Default to professional style
        return style_modifiers.get("professional", style_modifiers.get("minimalist", ""))

    def _get_category_specific_setup(
        self,
        template: Dict[str, Any],
        product_context: ProductContext
    ) -> Optional[Dict[str, str]]:
        """Get category-specific photography setup from template."""
        category_specific = template.get("category_specific", {})

        if product_context.category:
            category_key = product_context.category.value.lower()
            return category_specific.get(category_key)

        return None

    def _get_brand_voice_from_template(
        self,
        brand_context: Optional[BrandContext]
    ) -> Dict[str, Any]:
        """Get brand voice configuration from templates."""
        if not brand_context or not brand_context.brand_voice:
            return self.templates.get("brand_voice_templates", {}).get("professional", {})

        brand_voice_templates = self.templates.get("brand_voice_templates", {})
        return brand_voice_templates.get(brand_context.brand_voice.lower(), {})
    
    async def _generate_video_prompt(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext],
        market_context: Optional[MarketContext]
    ) -> GeneratedPrompt:
        """Generate professional video prompt."""
        
        # Video concept and narrative
        concept = self._create_video_concept(product_context, prompt_context, brand_context)
        
        # Shot list and sequences
        shot_list = self._generate_shot_list(product_context, prompt_context)
        
        # Audio and music direction
        audio_direction = self._get_audio_direction(product_context, brand_context, prompt_context)
        
        # Visual style and cinematography
        visual_style = self._get_video_visual_style(product_context, brand_context, prompt_context)
        
        # Construct video prompt
        prompt_parts = [
            f"Professional {prompt_context.media_type.replace('_', ' ')} featuring {product_context.title}",
            f"Concept: {concept}",
            f"Visual style: {visual_style}",
            f"Shot sequence: {shot_list}",
            f"Audio: {audio_direction}",
            f"Duration: {prompt_context.duration_seconds or 30} seconds"
        ]
        
        if prompt_context.platform:
            platform_specs = self.platform_specs.get(prompt_context.platform, {})
            if platform_specs:
                prompt_parts.append(f"Platform optimization: {platform_specs.get('video_style', '')}")
        
        main_prompt = ". ".join(prompt_parts)
        
        # Technical specifications for video
        technical_specs = {
            "resolution": "4K",
            "frame_rate": "30fps",
            "aspect_ratio": prompt_context.aspect_ratio,
            "duration": prompt_context.duration_seconds or 30,
            "format": "MP4",
            "codec": "H.264"
        }
        
        return GeneratedPrompt(
            main_prompt=main_prompt,
            technical_specs=technical_specs,
            estimated_quality_score=self._calculate_quality_score(product_context, prompt_context),
            target_keywords=self._extract_target_keywords(product_context)
        )
    
    async def _generate_copy_prompt(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext],
        market_context: Optional[MarketContext]
    ) -> GeneratedPrompt:
        """Generate professional copy prompt."""
        
        # Copy framework selection
        framework = self._select_copy_framework(prompt_context.media_type, product_context)
        
        # Tone and voice
        tone = self._determine_copy_tone(brand_context, prompt_context.platform, product_context.target_audience)
        
        # Key messages and benefits
        key_messages = self._extract_key_messages(product_context)
        
        # Call to action
        cta = prompt_context.call_to_action or self._generate_cta(product_context, prompt_context.platform)
        
        # Construct copy prompt
        prompt_parts = [
            f"Write {prompt_context.media_type.replace('_', ' ')} for {product_context.title}",
            f"Framework: {framework}",
            f"Tone: {tone}",
            f"Key benefits: {', '.join(key_messages)}",
            f"Target audience: {', '.join([aud.value for aud in product_context.target_audience])}",
            f"Call to action: {cta}"
        ]
        
        if brand_context:
            prompt_parts.append(f"Brand voice: {brand_context.brand_voice}")
            prompt_parts.append(f"Brand values: {', '.join(brand_context.brand_values)}")
        
        if prompt_context.platform:
            platform_specs = self.platform_specs.get(prompt_context.platform, {})
            char_limit = platform_specs.get("character_limit")
            if char_limit:
                prompt_parts.append(f"Character limit: {char_limit}")
        
        main_prompt = ". ".join(prompt_parts)
        
        return GeneratedPrompt(
            main_prompt=main_prompt,
            estimated_quality_score=self._calculate_quality_score(product_context, prompt_context),
            target_keywords=self._extract_target_keywords(product_context)
        )

    async def generate_text_prompt(
        self,
        product_context: ProductContext,
        content_type: str,
        brand_context: Optional[BrandContext] = None,
        language: str = "en",
        fallback_language: str = "en"
    ) -> str:
        """
        Generate a text prompt for text generation providers.

        Args:
            product_context: Product information and analysis
            content_type: Type of text content (product_description, marketing_copy, etc.)
            brand_context: Brand guidelines and preferences
            language: Target language for generation
            fallback_language: Fallback language if target is not available

        Returns:
            Formatted prompt string for text generation
        """
        # Build product context
        base_context = f"""
Product: {product_context.title}
Description: {product_context.description or 'Premium product'}
Category: {product_context.category.value if product_context.category else 'general'}
"""

        if product_context.key_features:
            base_context += f"Key Features: {', '.join(product_context.key_features)}\n"

        if product_context.target_audience:
            base_context += f"Target Audience: {', '.join([aud.value for aud in product_context.target_audience])}\n"

        if brand_context:
            base_context += f"Brand: {brand_context.name}\n"
            if brand_context.brand_voice:
                base_context += f"Brand Voice: {brand_context.brand_voice}\n"
            if brand_context.brand_values:
                base_context += f"Brand Values: {', '.join(brand_context.brand_values)}\n"

        # Add language-specific instructions
        from ..common.language_utils import get_language_instruction
        language_instruction = get_language_instruction(language, fallback_language)

        # Get template from loaded prompts
        text_templates = self.templates.get("text_templates", {})
        template = text_templates.get(content_type, text_templates.get("product_description", {}))

        if content_type == "product_description":
            structure = template.get("structure", {})
            prompt = f"""
{base_context}

{language_instruction}

Write a compelling product description that:
- {structure.get('hook', 'Attention-grabbing opening line')}
- {structure.get('features', 'Key features and specifications')}
- {structure.get('benefits', 'Customer benefits and value proposition')}
- {structure.get('social_proof', 'Trust signals and credibility')}
- {structure.get('call_to_action', 'Clear next step for customer')}
- Is optimized for e-commerce
- Includes relevant keywords naturally
- Is between 100-200 words

Focus on benefits over features and create desire for the product.
"""

        elif content_type == "marketing_copy":
            ad_copy = template.get("ad_copy", {})
            headline_formulas = ad_copy.get("headline_formulas", [])
            headline_examples = "\n".join(f"- {formula}" for formula in headline_formulas[:3])

            prompt = f"""
{base_context}

{language_instruction}

Create compelling marketing copy that:
- Grabs attention with a strong headline
- Highlights unique selling points
- Creates urgency or desire
- Includes a clear call-to-action
- Is suitable for ads or promotional materials
- Uses persuasive copywriting techniques

Headline formula examples:
{headline_examples}

Format: Headline + Body copy + Call-to-action
"""

        elif content_type == "social_caption":
            social_captions = template.get("social_captions", {})
            instagram = social_captions.get("instagram", {})
            elements = instagram.get("elements", [])

            prompt = f"""
{base_context}

{language_instruction}

Write an engaging social media caption that:
- Is attention-grabbing and shareable
- Uses relevant hashtags
- Encourages engagement
- Fits the platform's tone
- Is concise but impactful
- Includes emojis where appropriate

Key elements to include:
{chr(10).join(f"- {element}" for element in elements)}

Keep it under 150 characters for maximum impact.
"""

        elif content_type == "seo_snippet":
            seo_content = template.get("seo_content", {})
            meta_descriptions = seo_content.get("meta_descriptions", {})
            meta_titles = seo_content.get("product_titles", {})

            prompt = f"""
{base_context}

{language_instruction}

Create SEO-optimized content including:
- Meta title ({meta_titles.get('seo_best_practices', ['50-60 characters'])[0]})
- Meta description ({meta_descriptions.get('length', '150-160 characters')})
- Key SEO keywords
- Alt text for images
- Product schema markup suggestions

SEO best practices:
{chr(10).join(f"- {practice}" for practice in meta_titles.get('seo_best_practices', []))}

Focus on search visibility and click-through rates.
"""

        else:
            # Fallback to product description template
            structure = text_templates.get("product_description", {}).get("structure", {})
            prompt = f"""
{base_context}

{language_instruction}

Write a compelling product description that:
- {structure.get('hook', 'Attention-grabbing opening line')}
- {structure.get('features', 'Key features and specifications')}
- {structure.get('benefits', 'Customer benefits and value proposition')}
- {structure.get('social_proof', 'Trust signals and credibility')}
- {structure.get('call_to_action', 'Clear next step for customer')}

Focus on benefits over features and create desire for the product.
"""

        return prompt

    def _get_language_instruction(self, language: str, fallback_language: str) -> str:
        """Get language-specific instructions for text generation."""
        language_map = {
            "en": "Write in English using natural, engaging language.",
            "es": "Escribe en español usando un lenguaje natural y atractivo.",
            "fr": "Écrivez en français en utilisant un langage naturel et engageant.",
            "de": "Schreiben Sie auf Deutsch mit natürlicher, ansprechender Sprache.",
            "it": "Scrivi in italiano usando un linguaggio naturale e coinvolgente.",
            "pt": "Escreva em português usando linguagem natural e envolvente.",
            "ja": "自然で魅力的な言語を使って日本語で書いてください。",
            "ko": "자연스럽고 매력적인 언어를 사용하여 한국어로 작성하세요.",
            "zh": "使用自然、引人入胜的语言用中文写作。",
            "ar": "اكتب باللغة العربية باستخدام لغة طبيعية وجذابة.",
            "hi": "प्राकृतिक, आकर्षक भाषा का उपयोग करके हिंदी में लिखें।"
        }

        instruction = language_map.get(language)
        if not instruction and fallback_language != language:
            instruction = language_map.get(fallback_language, language_map["en"])

        if not instruction:
            instruction = language_map["en"]

        # Add fallback note if using different language
        if language != "en" and fallback_language == "en":
            instruction += f" If unable to generate in {language}, fall back to English."

        return instruction

    def _create_product_description(self, product_context: ProductContext) -> str:
        """Create detailed product description for prompts."""
        desc_parts = [product_context.title]

        if product_context.materials:
            desc_parts.append(f"made from {', '.join(product_context.materials)}")

        if product_context.colors:
            desc_parts.append(f"in {', '.join(product_context.colors)}")

        if product_context.style_keywords:
            desc_parts.append(f"with {', '.join(product_context.style_keywords[:3])} style")

        return " ".join(desc_parts)

    def _get_product_photography_style(
        self,
        product_context: ProductContext,
        brand_context: Optional[BrandContext],
        prompt_context: PromptContext
    ) -> Dict[str, Any]:
        """Get product photography style specifications."""

        # Base style from category
        category_styles = {
            "fashion_apparel": {
                "style": "fashion product",
                "camera_setup": "85mm lens, f/2.8",
                "background": "seamless white backdrop",
                "mood": "clean and professional",
                "camera": "medium format camera",
                "post_processing": "color correction and retouching"
            },
            "footwear": {
                "style": "product showcase",
                "camera_setup": "50mm lens, f/5.6",
                "background": "neutral gradient backdrop",
                "mood": "dynamic and appealing",
                "camera": "DSLR",
                "post_processing": "shadow enhancement"
            },
            "jewelry": {
                "style": "luxury product",
                "camera_setup": "macro lens, f/8",
                "background": "black velvet or white acrylic",
                "mood": "elegant and sophisticated",
                "camera": "macro photography setup",
                "post_processing": "highlight enhancement"
            }
        }

        base_style = category_styles.get(
            product_context.category.value,
            category_styles["fashion_apparel"]
        )

        # Modify based on brand context
        if brand_context:
            if brand_context.visual_style == ContentStyle.LUXURY:
                base_style["background"] = "premium textured backdrop"
                base_style["mood"] = "luxurious and exclusive"
            elif brand_context.visual_style == ContentStyle.MINIMALIST:
                base_style["background"] = "pure white seamless"
                base_style["mood"] = "clean and minimal"

        # Add style modifiers
        modifiers = ["professional lighting", "sharp details", "color accurate"]
        if product_context.price_tier == "luxury":
            modifiers.extend(["premium quality", "sophisticated composition"])

        base_style["modifiers"] = modifiers
        return base_style

    def _get_lifestyle_photography_style(
        self,
        product_context: ProductContext,
        brand_context: Optional[BrandContext],
        prompt_context: PromptContext
    ) -> Dict[str, Any]:
        """Get lifestyle photography style specifications."""

        # Lifestyle scenarios based on product
        lifestyle_styles = {
            "fashion_apparel": {
                "style": "lifestyle fashion",
                "camera_setup": "35mm lens, f/2.8",
                "background": "urban environment or natural setting",
                "mood": "authentic and aspirational",
                "scenario": "model wearing the item in daily life"
            },
            "footwear": {
                "style": "lifestyle action",
                "camera_setup": "24-70mm lens, f/4",
                "background": "relevant activity setting",
                "mood": "active and energetic",
                "scenario": "person using the product in context"
            }
        }

        base_style = lifestyle_styles.get(
            product_context.category.value,
            lifestyle_styles["fashion_apparel"]
        )

        # Adjust for target audience
        if TargetAudience.GEN_Z in product_context.target_audience:
            base_style["mood"] = "trendy and social media ready"
            base_style["background"] = "Instagram-worthy location"

        return base_style

    def _get_optimal_lighting(self, product_context: ProductContext, platform: Optional[Platform]) -> str:
        """Determine optimal lighting setup."""

        if product_context.category.value == "jewelry":
            return "soft box lighting with reflectors to minimize shadows and enhance sparkle"
        elif product_context.category.value == "fashion_apparel":
            return "even diffused lighting with key light and fill light"
        elif platform == Platform.INSTAGRAM:
            return "bright, even lighting optimized for mobile viewing"
        else:
            return "professional studio lighting with soft shadows"

    def _get_composition_guidelines(self, product_context: ProductContext, aspect_ratio: str) -> str:
        """Get composition guidelines based on product and format."""

        compositions = {
            "1:1": "centered composition with balanced negative space",
            "16:9": "rule of thirds with product positioned for visual impact",
            "9:16": "vertical composition optimized for mobile viewing",
            "4:5": "portrait orientation with product as focal point"
        }

        base_composition = compositions.get(aspect_ratio, compositions["1:1"])

        # Add product-specific composition notes
        if product_context.category.value == "footwear":
            base_composition += ", angled to show profile and sole details"
        elif product_context.category.value == "jewelry":
            base_composition += ", macro detail showing craftsmanship"

        return base_composition

    def _get_brand_visual_elements(self, brand_context: BrandContext) -> List[str]:
        """Extract brand visual elements for inclusion."""
        elements = []

        if brand_context.color_palette:
            elements.append(f"brand colors: {', '.join(brand_context.color_palette[:3])}")

        if brand_context.visual_style:
            elements.append(f"{brand_context.visual_style.value} aesthetic")

        return elements

    def _generate_photography_negative_prompt(self, product_context: ProductContext) -> str:
        """Generate negative prompt for photography with safety constraints."""
        # Base negative elements
        negative_elements = [
            "blurry", "low quality", "distorted", "oversaturated",
            "poor lighting", "cluttered background", "amateur",
            "pixelated", "noise", "artifacts"
        ]

        # Add safety and legal constraints
        safety_elements = [
            "logos", "brand names", "competitor brands", "watermarks",
            "text overlays", "NSFW content", "inappropriate content",
            "copyrighted material", "trademarked symbols"
        ]
        negative_elements.extend(safety_elements)

        # Add category-specific constraints
        if product_context.category:
            category_constraints = self._get_category_safety_constraints(product_context.category.value)
            negative_elements.extend(category_constraints)

        # Add product-specific negatives
        if product_context.category.value == "jewelry":
            negative_elements.extend(["scratches", "tarnish", "dust"])
        elif product_context.category.value == "fashion_apparel":
            negative_elements.extend(["wrinkles", "stains", "poor fit"])

        return ", ".join(negative_elements)

    def _get_category_safety_constraints(self, category: str) -> List[str]:
        """Get safety constraints specific to product category."""
        constraints_map = {
            "beauty_cosmetics": [
                "medical claims", "before/after comparisons", "unrealistic results"
            ],
            "children_baby": [
                "unsafe scenarios", "small parts emphasis", "inappropriate content"
            ],
            "electronics": [
                "false technical claims", "overheating implications", "safety violations"
            ],
            "luxury_goods": [
                "counterfeit implications", "cheap materials", "mass production"
            ],
            "fashion_apparel": [
                "inappropriate poses", "revealing content", "body shaming"
            ],
            "footwear": [
                "unsafe activities", "inappropriate use", "false performance claims"
            ]
        }

        return constraints_map.get(category, [])

    def _create_video_concept(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext]
    ) -> str:
        """Create video concept based on product and context."""

        concepts = {
            MediaType.PRODUCT_VIDEO: f"Product showcase highlighting key features and benefits of {product_context.title}",
            MediaType.LIFESTYLE_VIDEO: f"Lifestyle integration showing {product_context.title} in real-world use",
            MediaType.SOCIAL_VIDEO: f"Engaging social content featuring {product_context.title} with trending elements"
        }

        base_concept = concepts.get(prompt_context.media_type, concepts[MediaType.PRODUCT_VIDEO])

        # Add brand narrative if available
        if brand_context and brand_context.brand_values:
            base_concept += f" emphasizing {brand_context.brand_values[0]}"

        return base_concept

    def _generate_shot_list(self, product_context: ProductContext, prompt_context: PromptContext) -> str:
        """Generate shot list for video."""

        if prompt_context.media_type == MediaType.PRODUCT_VIDEO:
            shots = [
                "hero shot of product",
                "detail shots of key features",
                "360-degree rotation",
                "usage demonstration",
                "final branding shot"
            ]
        elif prompt_context.media_type == MediaType.LIFESTYLE_VIDEO:
            shots = [
                "lifestyle context establishment",
                "product introduction in scene",
                "usage in natural environment",
                "benefit demonstration",
                "call to action"
            ]
        else:  # Social video
            shots = [
                "attention-grabbing opener",
                "quick product reveal",
                "key benefit highlight",
                "social proof or testimonial",
                "strong call to action"
            ]

        return ", ".join(shots)

    def _get_audio_direction(
        self,
        product_context: ProductContext,
        brand_context: Optional[BrandContext],
        prompt_context: PromptContext
    ) -> str:
        """Get audio direction for video."""

        if prompt_context.platform == Platform.TIKTOK:
            return "trending background music with upbeat tempo"
        elif brand_context and brand_context.brand_voice == "luxury":
            return "sophisticated instrumental music with premium feel"
        elif product_context.category.value == "sports_fitness":
            return "energetic music matching active lifestyle"
        else:
            return "professional background music matching brand tone"

    def _get_video_visual_style(
        self,
        product_context: ProductContext,
        brand_context: Optional[BrandContext],
        prompt_context: PromptContext
    ) -> str:
        """Get video visual style direction."""

        if brand_context:
            if brand_context.visual_style == ContentStyle.MINIMALIST:
                return "clean, minimal aesthetic with smooth transitions"
            elif brand_context.visual_style == ContentStyle.LUXURY:
                return "premium cinematography with elegant movements"

        if prompt_context.platform == Platform.TIKTOK:
            return "dynamic, fast-paced editing with trending visual effects"
        elif prompt_context.platform == Platform.INSTAGRAM:
            return "Instagram-optimized visuals with engaging transitions"

        return "professional cinematography with smooth camera movements"

    def _select_copy_framework(self, media_type: MediaType, product_context: ProductContext) -> str:
        """Select appropriate copywriting framework."""

        frameworks = {
            MediaType.MARKETING_COPY: "AIDA (Attention, Interest, Desire, Action)",
            MediaType.SOCIAL_CAPTION: "Hook, Value, Call-to-Action",
            MediaType.PRODUCT_DESCRIPTION: "Features, Benefits, Social Proof"
        }

        return frameworks.get(media_type, "Problem-Solution-Benefit")

    def _determine_copy_tone(
        self,
        brand_context: Optional[BrandContext],
        platform: Optional[Platform],
        target_audience: List[TargetAudience]
    ) -> str:
        """Determine appropriate tone for copy."""

        if brand_context:
            return brand_context.brand_voice

        if platform == Platform.TIKTOK and TargetAudience.GEN_Z in target_audience:
            return "casual, trendy, and authentic"
        elif TargetAudience.LUXURY_BUYERS in target_audience:
            return "sophisticated and exclusive"
        elif TargetAudience.PROFESSIONALS in target_audience:
            return "professional and authoritative"

        return "friendly and approachable"

    def _extract_key_messages(self, product_context: ProductContext) -> List[str]:
        """Extract key messages from product context."""
        messages = []

        if product_context.key_features:
            messages.extend(product_context.key_features[:3])

        if product_context.use_cases:
            messages.append(f"Perfect for {product_context.use_cases[0]}")

        if product_context.price_tier == "budget":
            messages.append("Great value")
        elif product_context.price_tier == "luxury":
            messages.append("Premium quality")

        return messages[:5]  # Limit to top 5 messages

    def _generate_cta(self, product_context: ProductContext, platform: Optional[Platform]) -> str:
        """Generate appropriate call to action."""

        if platform == Platform.INSTAGRAM:
            return "Shop now - link in bio"
        elif platform == Platform.TIKTOK:
            return "Get yours today!"
        elif product_context.price_tier == "luxury":
            return "Experience luxury - shop now"
        else:
            return "Shop now and save"

    def _calculate_quality_score(self, product_context: ProductContext, prompt_context: PromptContext) -> float:
        """Calculate estimated quality score for the prompt."""
        score = 0.5  # Base score

        # Add points for rich product context
        if product_context.key_features:
            score += 0.1
        if product_context.style_keywords:
            score += 0.1
        if product_context.materials:
            score += 0.1

        # Add points for specific targeting
        if product_context.target_audience:
            score += 0.1

        # Add points for platform optimization
        if prompt_context.platform:
            score += 0.1

        return min(score, 1.0)

    def _extract_target_keywords(self, product_context: ProductContext) -> List[str]:
        """Extract target keywords for SEO and optimization."""
        keywords = [product_context.title.lower()]

        if product_context.category:
            keywords.append(product_context.category.value.replace("_", " "))

        if product_context.style_keywords:
            keywords.extend(product_context.style_keywords)

        if product_context.materials:
            keywords.extend(product_context.materials)

        return list(set(keywords))  # Remove duplicates

    def _load_photography_styles(self) -> Dict[str, Any]:
        """Load photography style configurations."""
        return {
            "product": {"lighting": "studio", "background": "neutral"},
            "lifestyle": {"lighting": "natural", "background": "environmental"}
        }

    def _load_video_templates(self) -> Dict[str, Any]:
        """Load video template configurations."""
        return {
            "product_showcase": {"duration": 30, "shots": 5},
            "lifestyle": {"duration": 45, "shots": 7}
        }

    def _load_copy_frameworks(self) -> Dict[str, Any]:
        """Load copywriting framework templates."""
        return {
            "AIDA": {"structure": ["attention", "interest", "desire", "action"]},
            "PAS": {"structure": ["problem", "agitation", "solution"]}
        }

    def _load_platform_specifications(self) -> Dict[Platform, Dict[str, Any]]:
        """Load platform-specific specifications."""
        return {
            Platform.INSTAGRAM: {
                "character_limit": 2200,
                "video_style": "square or vertical",
                "hashtag_limit": 30
            },
            Platform.TIKTOK: {
                "character_limit": 300,
                "video_style": "vertical, fast-paced",
                "hashtag_limit": 100
            },
            Platform.FACEBOOK: {
                "character_limit": 63206,
                "video_style": "horizontal or square",
                "hashtag_limit": 30
            }
        }


# Create service instance
prompt_engine = PromptEngine()
