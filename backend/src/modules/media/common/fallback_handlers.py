"""
Common fallback handlers for media providers.
Standardized fallback logic for when primary generation fails.
"""

import logging
from typing import Dict, Any, Optional

from ..engines.prompt_engine import MediaType

logger = logging.getLogger(__name__)


def create_fallback_prompt(
    request: 'ProviderMediaRequest',
    variant: Dict[str, Any],
    media_type: MediaType
) -> Dict[str, Any]:
    """
    Create fallback prompt when prompt engine fails.

    Args:
        request: Provider media request
        variant: Variant configuration
        media_type: Type of media being generated

    Returns:
        Fallback prompt configuration
    """
    base_prompt = f"Professional {variant.get('style_type', 'product')} photograph of {request.product_title}"

    if request.product_description:
        base_prompt += f", {request.product_description[:100]}"

    # Add style-specific additions
    style_additions = {
        "product_photography": "clean white background, studio lighting, high resolution, commercial photography",
        "lifestyle": "lifestyle setting, natural lighting, in-use context, appealing environment",
        "minimalist": "minimal background, clean composition, simple elegant styling",
        "social_media": "trendy, eye-catching, social media optimized, vibrant colors",
        "luxury": "luxury setting, sophisticated lighting, premium presentation, high-end styling",
        "commercial": "commercial photography, professional studio setup, marketing ready"
    }

    style_addition = style_additions.get(variant.get("style_type"), "professional photography")
    base_prompt += f", {style_addition}, 8K resolution, sharp focus, professional lighting"

    return {
        "prompt": base_prompt,
        "negative_prompt": "blurry, low quality, distorted, oversaturated, poor lighting, amateur",
        "style": variant.get("style_type", "professional"),
        "variant_name": variant.get("variant_name", "fallback"),
        "style_type": variant.get("style_type", "professional"),
        "quality_score": 0.7
    }


def create_fallback_concept(
    request: 'ProviderMediaRequest',
    variant: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Create fallback video concept when prompt engine fails.

    Args:
        request: Provider media request
        variant: Variant configuration

    Returns:
        Fallback concept configuration
    """
    base_prompt = f"Professional {variant.get('concept_type', 'product showcase')} video of {request.product_title}"

    if request.product_description:
        base_prompt += f". {request.product_description[:100]}"

    base_prompt += f". High-quality {variant.get('duration', 30)}-second video with smooth camera movements and professional presentation."

    # Add concept-specific styling
    concept_styles = {
        "product_showcase": "clean, professional, well-lit, showcasing product features",
        "lifestyle_integration": "natural, authentic, integrated into daily life",
        "social_media": "engaging, trendy, optimized for social platforms",
        "360_showcase": "comprehensive, detailed, showing all product angles"
    }

    style = concept_styles.get(variant.get("concept_type"), "professional")
    base_prompt += f" Style: {style}."

    return {
        "prompt": base_prompt,
        "concept_type": variant.get("concept_type", "product_showcase"),
        "variant_name": variant.get("variant_name", "fallback_concept"),
        "aspect_ratio": variant.get("aspect_ratio", "16:9"),
        "duration": variant.get("duration", 30),
        "style_type": _get_fallback_style_type(variant.get("concept_type")),
        "camera_movements": _get_fallback_camera_movements(variant.get("concept_type")),
        "lighting": _get_fallback_lighting(variant.get("concept_type")),
        "music_style": _get_fallback_music_style(variant.get("concept_type")),
        "quality_score": 0.7
    }


def _get_fallback_style_type(concept_type: str) -> str:
    """Get fallback style type for concept."""
    style_map = {
        "product_showcase": "professional_showcase",
        "lifestyle_integration": "lifestyle_natural",
        "social_media": "dynamic_engaging",
        "360_showcase": "product_rotation"
    }
    return style_map.get(concept_type, "professional")


def _get_fallback_camera_movements(concept_type: str) -> str:
    """Get fallback camera movements for concept."""
    movement_map = {
        "product_showcase": "smooth zoom and pan",
        "lifestyle_integration": "handheld natural movement",
        "social_media": "dynamic quick cuts",
        "360_showcase": "360-degree rotation"
    }
    return movement_map.get(concept_type, "smooth")


def _get_fallback_lighting(concept_type: str) -> str:
    """Get fallback lighting for concept."""
    lighting_map = {
        "product_showcase": "professional studio lighting",
        "lifestyle_integration": "natural ambient lighting",
        "social_media": "bright vibrant lighting",
        "360_showcase": "even diffused lighting"
    }
    return lighting_map.get(concept_type, "professional")


def _get_fallback_music_style(concept_type: str) -> str:
    """Get fallback music style for concept."""
    music_map = {
        "product_showcase": "upbeat corporate",
        "lifestyle_integration": "ambient lifestyle",
        "social_media": "trendy upbeat",
        "360_showcase": "minimal ambient"
    }
    return music_map.get(concept_type, "upbeat")


def create_fallback_text_content(
    request: 'ProviderMediaRequest',
    content_type: str
) -> Dict[str, Any]:
    """
    Create fallback text content when generation fails.

    Args:
        request: Provider media request
        content_type: Type of text content

    Returns:
        Fallback text content
    """
    base_content = f"This is a {content_type} for {request.product_title}."

    if request.product_description:
        base_content += f" {request.product_description[:200]}"

    # Add content-type specific enhancements
    if content_type == "product_description":
        base_content = f"Discover {request.product_title} - {request.product_description or 'a premium product'}. This high-quality item offers exceptional value and performance."
    elif content_type == "marketing_copy":
        base_content = f"Introducing {request.product_title} - the perfect solution for your needs. Experience quality and reliability like never before."
    elif content_type == "social_caption":
        base_content = f"✨ Check out this amazing {request.product_title}! ✨ {request.product_description or ''} #product #amazing"

    return {
        "content_type": content_type,
        "text": base_content,
        "word_count": len(base_content.split()),
        "character_count": len(base_content),
        "variant_name": f"{content_type}_fallback",
        "quality_score": 0.6
    }


def handle_generation_fallback(
    error: Exception,
    request: 'ProviderMediaRequest',
    context: Dict[str, Any]
) -> 'ProviderMediaResult':
    """
    Handle generation failure with appropriate fallback.

    Args:
        error: The error that occurred
        request: Original provider request
        context: Additional context information

    Returns:
        ProviderMediaResult with fallback content or error
    """
    logger.warning(f"Generation failed, attempting fallback: {error}")

    from ..schemas import ProviderMediaResult

    # Try to create basic fallback based on media type
    if request.media_type == "image":
        fallback_result = _create_image_fallback(request)
    elif request.media_type == "video":
        fallback_result = _create_video_fallback(request)
    elif request.media_type == "text":
        fallback_result = _create_text_fallback(request)
    else:
        return ProviderMediaResult(
            success=False,
            error_message=f"Generation failed and no fallback available: {str(error)}"
        )

    logger.info("Fallback generation completed")
    return fallback_result


def _create_image_fallback(request: 'ProviderMediaRequest') -> 'ProviderMediaResult':
    """Create basic image fallback."""
    from ..schemas import ProviderMediaResult

    fallback_image = {
        "image_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkE0IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiI+UHJvZHVjdCBJbWFnZTwvdGV4dD4KPC9zdmc+",
        "thumbnail_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzlCOUI5QjQiIGZvbnQtZmFtaWx5PSJzYW5zLXNlcmlmIiBmb250LXNpemU9IjEwIj5JbWc8L3RleHQ+Cjwvc3ZnPg==",
        "width": 400,
        "height": 400,
        "style": "fallback",
        "variant_name": "fallback_image",
        "generation_metadata": {
            "provider": "fallback",
            "is_fallback": True,
            "error": "Primary generation failed"
        }
    }

    return ProviderMediaResult(
        success=True,
        provider_job_id="fallback_image_generation",
        images=[fallback_image],
        estimated_completion_time=0
    )


def _create_video_fallback(request: 'ProviderMediaRequest') -> 'ProviderMediaResult':
    """Create basic video fallback."""
    from ...schemas import ProviderMediaResult

    fallback_video = {
        "video_url": "data:video/mp4;base64,AAAAHGZ0eXBtcDQyAAACAGlzb21pc28yYXZjMQAAAAhmcmVlAAAGF21kYXQ=",  # Minimal MP4 header
        "thumbnail_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkE0IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiI+VmlkZW88L3RleHQ+Cjwvc3ZnPg==",
        "duration": 5,
        "resolution": "400x400",
        "aspect_ratio": "1:1",
        "style_type": "fallback",
        "variant_name": "fallback_video",
        "generation_metadata": {
            "provider": "fallback",
            "is_fallback": True,
            "error": "Primary generation failed"
        }
    }

    return ProviderMediaResult(
        success=True,
        provider_job_id="fallback_video_generation",
        variants=[fallback_video],
        estimated_completion_time=0
    )


def _create_text_fallback(request: 'ProviderMediaRequest') -> 'ProviderMediaResult':
    """Create basic text fallback."""
    from ...schemas import ProviderMediaResult

    fallback_text = create_fallback_text_content(request, "product_description")

    return ProviderMediaResult(
        success=True,
        provider_job_id="fallback_text_generation",
        variants=[fallback_text],
        estimated_completion_time=0
    )