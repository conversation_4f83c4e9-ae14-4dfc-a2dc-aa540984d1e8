[tool:pytest]
# Pytest configuration for ProductVideo backend

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --asyncio-mode=auto

# Python path configuration
pythonpath = src

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests (may take more than 1 second)
    external: Tests that require external services
    load: Load/performance tests
    shopify: Tests that interact with Shopify API
    stripe: Tests that interact with Stripe API
    video: Tests related to video generation
    analytics: Tests related to analytics
    billing: Tests related to billing
    auth: Tests related to authentication
    admin: Tests related to admin functionality

# Asyncio configuration
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# Uncomment to run tests in parallel (requires pytest-xdist)
# -n auto
